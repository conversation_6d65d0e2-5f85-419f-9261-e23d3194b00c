/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #06b6d4;
    
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-dark: #0f172a;
    --bg-card: #ffffff;
    
    /* Text Colors */
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #94a3b8;
    --text-inverse: #ffffff;
    
    /* Border Colors */
    --border-color: #e2e8f0;
    --border-hover: #cbd5e1;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    
    /* Fonts */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    overflow: hidden;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    color: var(--text-inverse);
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--text-inverse);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-lg);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content h2 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.loading-content p {
    font-size: var(--font-size-base);
    opacity: 0.9;
}

/* Main App Layout */
.main-app {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.app-header {
    background: var(--bg-card);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-md) var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-sm);
    z-index: 100;
}

.header-left .app-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.header-left .app-title i {
    color: var(--primary-color);
}

.header-center {
    display: flex;
    align-items: center;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    background: var(--bg-tertiary);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--danger-color);
    animation: pulse 2s infinite;
}

.status-indicator.connected {
    background: var(--success-color);
}

.status-indicator.connecting {
    background: var(--warning-color);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.auth-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
}

.auth-status .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--danger-color);
}

.auth-status .status-indicator.online {
    background: var(--success-color);
    animation: pulse 2s infinite;
}

.auth-status .status-indicator.offline {
    background: var(--danger-color);
}

.trading-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    background: var(--bg-tertiary);
}

.trading-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--secondary-color);
}

.trading-indicator.running {
    background: var(--success-color);
    animation: pulse 2s infinite;
}

.trading-indicator.stopped {
    background: var(--danger-color);
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: 240px;
    background: var(--bg-card);
    border-right: 1px solid var(--border-color);
    padding: var(--spacing-lg) 0;
    overflow-y: auto;
}

.nav-menu {
    list-style: none;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    color: var(--text-secondary);
    font-weight: 500;
}

.nav-item:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.nav-item.active {
    background: var(--primary-color);
    color: var(--text-inverse);
    border-right: 3px solid var(--primary-dark);
}

.nav-item i {
    width: 20px;
    text-align: center;
}

/* Content Area */
.content-area {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
    background: var(--bg-secondary);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Cards */
.card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-lg);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-tertiary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.card-body {
    padding: var(--spacing-lg);
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

/* Market Overview */
.market-indices {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.index-item {
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    text-align: center;
}

.index-name {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.index-price {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.index-change {
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.index-change.positive {
    color: var(--success-color);
}

.index-change.negative {
    color: var(--danger-color);
}

/* Quick Stats */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-md);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.stat-value {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.btn:hover {
    background: var(--border-hover);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--text-inverse);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-success {
    background: var(--success-color);
    color: var(--text-inverse);
    border-color: var(--success-color);
}

.btn-danger {
    background: var(--danger-color);
    color: var(--text-inverse);
    border-color: var(--danger-color);
}

.btn-warning {
    background: var(--warning-color);
    color: var(--text-inverse);
    border-color: var(--warning-color);
}

.btn-icon {
    padding: var(--spacing-sm);
    width: 36px;
    height: 36px;
}

/* Control Buttons */
.control-buttons {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.control-buttons .btn {
    flex: 1;
    min-width: 140px;
}

/* Activity List */
.activity-list {
    max-height: 200px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-time {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    min-width: 60px;
}

.activity-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* Market Time */
.market-time {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--bg-tertiary);
}

.modal-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
}

.modal-body {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.close-modal {
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.close-modal:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

/* Settings Styles */
.settings-container {
    display: flex;
    height: 500px;
}

.settings-tabs {
    width: 200px;
    background: var(--bg-tertiary);
    border-right: 1px solid var(--border-color);
    padding: var(--spacing-md) 0;
}

.settings-tab {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    transition: all var(--transition-fast);
    color: var(--text-secondary);
    font-weight: 500;
}

.settings-tab:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.settings-tab.active {
    background: var(--primary-color);
    color: var(--text-inverse);
}

.settings-tab i {
    width: 16px;
    text-align: center;
}

.settings-content {
    flex: 1;
    overflow-y: auto;
}

.settings-tab-content {
    display: none;
    padding: var(--spacing-lg);
}

.settings-tab-content.active {
    display: block;
}

.settings-tab-content h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.settings-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

/* Form Styles */
.settings-form {
    max-width: 500px;
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group small {
    display: block;
    margin-top: var(--spacing-xs);
    color: var(--text-muted);
    font-size: var(--font-size-xs);
}

.form-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

/* Checkbox Styles */
.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-weight: 500;
    color: var(--text-primary);
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    position: relative;
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Auth Section */
.auth-section {
    margin-top: var(--spacing-2xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.auth-section h5 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.totp-form {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.totp-form input {
    flex: 1;
    max-width: 200px;
    text-align: center;
    font-family: monospace;
    font-size: var(--font-size-lg);
    letter-spacing: 2px;
}

.auth-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    background: var(--bg-tertiary);
}

.auth-status .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--danger-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 200px;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .control-buttons {
        flex-direction: column;
    }

    .control-buttons .btn {
        min-width: auto;
    }

    .modal-content {
        width: 95%;
        max-height: 95vh;
    }

    .settings-container {
        flex-direction: column;
        height: auto;
    }

    .settings-tabs {
        width: 100%;
        display: flex;
        overflow-x: auto;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }

    .settings-tab {
        white-space: nowrap;
        min-width: 150px;
    }

    .form-actions {
        flex-direction: column;
    }

    .totp-form {
        flex-direction: column;
    }
}

/* Trading Interface Styles */
.trading-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-lg);
    height: calc(100vh - 200px);
}

.option-chain-card {
    min-height: 600px;
}

.option-chain-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
}

.symbol-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.current-price {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.price-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.price-value {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.price-change {
    font-size: var(--font-size-sm);
    font-weight: 500;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
}

.price-change.positive {
    color: var(--success-color);
    background: rgba(16, 185, 129, 0.1);
}

.price-change.negative {
    color: var(--danger-color);
    background: rgba(239, 68, 68, 0.1);
}

.expiry-selector {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.expiry-selector label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    white-space: nowrap;
}

.expiry-selector select {
    min-width: 200px;
}

.chain-controls {
    display: flex;
    gap: var(--spacing-sm);
}

/* Option Chain Table */
.option-chain-table-container {
    overflow-x: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
}

.option-chain-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--font-size-sm);
}

.option-chain-table th,
.option-chain-table td {
    padding: var(--spacing-sm);
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

.option-chain-table th {
    background: var(--bg-tertiary);
    font-weight: 600;
    color: var(--text-primary);
    position: sticky;
    top: 0;
    z-index: 10;
}

.call-header {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.put-header {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.strike-header {
    background: var(--primary-color);
    color: var(--text-inverse);
}

.sub-header th {
    background: var(--bg-secondary);
    font-size: var(--font-size-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
}

.strike-price {
    background: var(--bg-tertiary);
    font-weight: 600;
    color: var(--text-primary);
}

.strike-price.atm {
    background: var(--primary-color);
    color: var(--text-inverse);
}

.atm-strike {
    background: rgba(37, 99, 235, 0.05);
}

.atm-strike .strike-price {
    background: var(--primary-color);
    color: var(--text-inverse);
}

/* Option Chain Cell Styles */
.ltp-cell {
    font-weight: 600;
}

.ltp-cell.positive {
    color: var(--success-color);
}

.ltp-cell.negative {
    color: var(--danger-color);
}

.change-cell.positive {
    color: var(--success-color);
}

.change-cell.negative {
    color: var(--danger-color);
}

.oi-cell,
.volume-cell {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.iv-cell {
    font-size: var(--font-size-xs);
}

.action-cell {
    padding: var(--spacing-xs);
}

.action-cell .btn {
    margin: 0 2px;
    padding: 2px 6px;
    font-size: 10px;
}

.btn-xs {
    padding: 2px 6px;
    font-size: 10px;
    border-radius: var(--radius-sm);
}

.loading-row {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.loading-row .loading-spinner {
    width: 20px;
    height: 20px;
    margin: 0 auto var(--spacing-sm);
}

/* Order Form Styles */
.order-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.form-row .form-group:only-child {
    grid-column: 1 / -1;
}

.schedule-section {
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.order-summary {
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid var(--border-color);
}

.summary-row:last-child {
    border-bottom: none;
    font-weight: 600;
    color: var(--primary-color);
}

/* Scheduled Orders */
.scheduled-orders-card {
    max-height: 400px;
}

.scheduled-orders-list {
    max-height: 300px;
    overflow-y: auto;
}

.empty-state {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-muted);
}

.empty-state i {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-sm);
    opacity: 0.5;
}

.scheduled-order-item {
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-sm);
    background: var(--bg-primary);
}

.scheduled-order-item:last-child {
    margin-bottom: 0;
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.order-symbol {
    font-weight: 600;
    color: var(--text-primary);
}

.order-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.order-status.pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.order-status.executed {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.order-status.cancelled {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.order-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.order-detail {
    display: flex;
    flex-direction: column;
}

.detail-label {
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
}

.detail-value {
    color: var(--text-primary);
    font-weight: 500;
}

/* Responsive Trading Layout */
@media (max-width: 1200px) {
    .trading-layout {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .option-chain-table-container {
        overflow-x: scroll;
    }

    .form-row {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .option-chain-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }

    .symbol-info {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .chain-controls {
        justify-content: center;
    }

    .option-chain-table {
        font-size: var(--font-size-xs);
    }

    .option-chain-table th,
    .option-chain-table td {
        padding: var(--spacing-xs);
    }
}
