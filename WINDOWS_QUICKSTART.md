# 🪟 Windows Quick Start Guide

## 🚀 **5-Minute Setup for Windows**

### **Prerequisites**
- Windows 10/11
- Node.js 16+ installed ([Download here](https://nodejs.org/))
- Kotak Neo Trade account with API access

### **Step 1: Download & Setup**
```cmd
# Open Command Prompt or PowerShell
# Navigate to the project folder
cd path\to\nifty-options-trading-bot

# Run the automated setup
setup.bat
```

**OR manually:**
```cmd
npm install
npm test
npm start
```

### **Step 2: Configure Credentials**
1. **Application will open automatically**
2. **Click ⚙️ Settings** (top-right corner)
3. **Go to "API Credentials" tab**
4. **Fill in your details:**

```
User ID: [Your Kotak trading ID]
Password: [Your Kotak password]
Mobile: [10-digit mobile number]
Consumer Key: [From Kotak API portal]
Consumer Secret: [From Kotak API portal]
Environment: Production (for live trading)
```

5. **Click "Save Credentials"**
6. **Click "Test Connection"**

### **Step 3: Login & Start Trading**
1. **Enter 6-digit TOTP code** from your authenticator app
2. **Click "Authenticate"**
3. **You're ready to trade!** 🎉

## 📱 **Where to Get Credentials**

### **Kotak Neo Trade API Access**
1. **Login** to [Kotak Neo Trade](https://neo.kotaksecurities.com)
2. **Go to Settings → API Management**
3. **Apply for API access** (if not done)
4. **Get Consumer Key & Secret**

### **TOTP Setup**
1. **Download Google Authenticator** or similar app
2. **Scan QR code** provided by Kotak
3. **App generates 6-digit codes** every 30 seconds

## 🎯 **Quick Trading Guide**

### **View Market Data**
- **Dashboard**: Shows Nifty 50 & Bank Nifty live prices
- **Trading Tab**: Interactive option chain with live prices

### **Place Orders**
1. **Go to Trading tab**
2. **Select Nifty or Bank Nifty**
3. **Choose expiry date**
4. **Click Buy/Sell** on any option
5. **Set quantity, stop-loss, target**
6. **Click "Place Order"**

### **Monitor Positions**
- **Portfolio tab**: Live P&L tracking
- **Dashboard**: Overall statistics
- **Automatic stop-loss/target** execution

## ⚠️ **Important Notes**

### **Security**
- ✅ All credentials stored securely on your PC
- ✅ No data sent to external servers
- ✅ Use strong passwords

### **Trading Safety**
- 🧪 **Test in UAT environment first**
- 💰 **Start with small amounts**
- 📊 **Set proper stop-losses**
- ⏰ **Monitor during market hours**

### **Risk Management**
- **Daily loss limit**: Set in Settings → Risk Management
- **Position size limits**: Configured automatically
- **Auto square-off**: Before market close

## 🔧 **Troubleshooting**

### **Application Won't Start**
```cmd
# Check Node.js version
node --version

# Should be 16.0.0 or higher
# If not, download from nodejs.org

# Reinstall dependencies
npm install
```

### **"Invalid Credentials" Error**
- ✅ Double-check all credential fields
- ✅ Ensure API access is approved by Kotak
- ✅ Try "Test Connection" button

### **TOTP Authentication Fails**
- ✅ Check device time is correct
- ✅ Use fresh 6-digit code
- ✅ Ensure authenticator app is synced

### **No Market Data**
- ✅ Check internet connection
- ✅ Verify market hours (9:15 AM - 3:30 PM)
- ✅ Restart application

## 📞 **Need Help?**

### **Technical Issues**
- Check console for error messages
- Restart the application
- Reinstall dependencies: `npm install`

### **API/Trading Issues**
- **Kotak Support**: 1860-266-0808
- **Email**: <EMAIL>

## 🎯 **Pro Tips**

### **For Best Performance**
- ✅ Close unnecessary applications
- ✅ Use wired internet connection
- ✅ Keep application running during trading hours
- ✅ Monitor system resources

### **Trading Tips**
- 📈 **Start with paper trading** (UAT environment)
- 💡 **Use limit orders** for better fills
- ⏱️ **Schedule orders** for exact timing
- 📊 **Monitor option Greeks** for better decisions

## 🚀 **Advanced Features**

### **Scheduled Trading**
- Set exact time for order execution
- Useful for opening/closing positions

### **Auto Square-off**
- Automatically closes positions before market close
- Configurable time (default: 3:20 PM)

### **Risk Limits**
- Daily P&L limits
- Position size controls
- Maximum trades per day

### **Real-time Monitoring**
- Live option chain updates
- Position P&L tracking
- Market data streaming

## ✅ **Ready Checklist**

Before live trading:
- [ ] Application starts successfully
- [ ] Credentials configured and tested
- [ ] TOTP authentication works
- [ ] Market data is flowing
- [ ] Test order placed successfully (UAT)
- [ ] Risk settings configured
- [ ] Stop-loss/target settings verified

**You're all set! Happy Trading! 📈💰**

---

**Remember**: Always trade responsibly and never risk more than you can afford to lose.
