const axios = require('axios');
const Store = require('electron-store');
const crypto = require('crypto');

/**
 * Kotak Neo Trade API Authentication Service
 * Implements the correct 3-step authentication flow:
 * 1. Get Client Access Token (Consumer Key/Secret)
 * 2. Get View Token (UCC + Mobile + TOTP)
 * 3. Get Trading Token (MPIN) - Optional for trading
 */
class KotakAuthService {
    constructor() {
        this.store = new Store({
            name: 'kotak-auth-config',
            encryptionKey: this.getEncryptionKey()
        });
        
        // API URLs
        this.tokenURL = 'https://napi.kotaksecurities.com/oauth2/token';
        this.loginURL = 'https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/login';
        this.validateURL = 'https://gw-napi.kotaksecurities.com/login/1.0/login/v6/totp/validate';
        
        // Authentication state
        this.isAuthenticated = false;
        this.canTrade = false;
        
        // Tokens
        this.clientAccessToken = null;
        this.viewToken = null;
        this.tradingToken = null;
        this.accessToken = null; // Current active token
        
        // Session data
        this.sessionId = null;
        this.requestId = null;
        this.ucc = null;
        this.greetingName = null;
        this.sessionExpiry = null;
        
        // Load existing session
        this.loadStoredTokens();
    }

    /**
     * Get encryption key for secure storage
     */
    getEncryptionKey() {
        const os = require('os');
        return crypto.createHash('sha256').update(os.hostname() + os.userInfo().username).digest('hex');
    }

    /**
     * Save credentials securely
     */
    saveCredentials(credentials) {
        try {
            // Validate required fields
            const required = ['consumerKey', 'consumerSecret', 'ucc', 'mobileNumber'];
            for (const field of required) {
                if (!credentials[field]) {
                    throw new Error(`Missing required field: ${field}`);
                }
            }

            // Validate mobile number format (Indian)
            if (!/^[6-9]\d{9}$/.test(credentials.mobileNumber)) {
                throw new Error('Invalid mobile number format. Use 10-digit Indian mobile number.');
            }

            // Validate UCC format
            if (!/^[A-Z0-9]{5,10}$/.test(credentials.ucc)) {
                throw new Error('Invalid UCC format. UCC should be 5-10 alphanumeric characters.');
            }

            this.store.set('credentials', credentials);
            console.log('Credentials saved successfully');
            return true;
        } catch (error) {
            console.error('Failed to save credentials:', error);
            throw error;
        }
    }

    /**
     * Get stored credentials
     */
    getCredentials() {
        return this.store.get('credentials');
    }

    /**
     * Clear stored credentials
     */
    clearCredentials() {
        this.store.delete('credentials');
        this.clearStoredTokens();
    }

    /**
     * Step 1: Get Client Access Token using Consumer Key/Secret
     */
    async getClientAccessToken() {
        try {
            const credentials = this.getCredentials();
            if (!credentials || !credentials.consumerKey || !credentials.consumerSecret) {
                throw new Error('Consumer Key and Secret are required');
            }

            // Create Base64 encoded authorization header
            const auth = Buffer.from(`${credentials.consumerKey}:${credentials.consumerSecret}`).toString('base64');

            console.log('Getting client access token...');
            const response = await axios.post(
                this.tokenURL,
                'grant_type=client_credentials',
                {
                    headers: {
                        'Authorization': `Basic ${auth}`,
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    timeout: 15000
                }
            );

            if (response.data && response.data.access_token) {
                this.clientAccessToken = response.data.access_token;
                console.log('✓ Client access token obtained');
                return this.clientAccessToken;
            } else {
                throw new Error('Failed to get client access token');
            }
        } catch (error) {
            console.error('Client access token error:', error);
            if (error.response?.status === 401) {
                throw new Error('Invalid Consumer Key or Secret');
            }
            throw error;
        }
    }

    /**
     * Step 2: Authenticate with TOTP (Get View Token)
     */
    async authenticate(totp) {
        try {
            const credentials = this.getCredentials();
            if (!credentials) {
                throw new Error('No credentials found. Please configure API credentials first.');
            }

            // Validate TOTP format
            if (!/^\d{6}$/.test(totp)) {
                throw new Error('TOTP must be a 6-digit number');
            }

            // First get client access token
            const clientToken = await this.getClientAccessToken();

            console.log('Authenticating with TOTP...');
            
            // Step 2: Get View Token with TOTP
            const loginData = {
                mobileNumber: `+91${credentials.mobileNumber}`, // Add country code
                ucc: credentials.ucc,
                totp: totp
            };

            const response = await axios.post(
                this.loginURL,
                loginData,
                {
                    headers: {
                        'Authorization': `Bearer ${clientToken}`,
                        'neo-fin-key': 'neotradeapi',
                        'Content-Type': 'application/json'
                    },
                    timeout: 15000
                }
            );

            if (response.data && response.data.data && response.data.data.token) {
                // Store session details
                this.viewToken = response.data.data.token;
                this.sessionId = response.data.data.sid;
                this.requestId = response.data.data.rid;
                this.ucc = response.data.data.ucc;
                this.greetingName = response.data.data.greetingName;
                this.accessToken = this.viewToken; // Use view token as active token
                this.isAuthenticated = true;
                this.sessionExpiry = new Date(Date.now() + (8 * 60 * 60 * 1000)); // 8 hours

                this.saveTokens();
                console.log('✓ Authentication successful (View mode)');

                return {
                    success: true,
                    message: 'Authentication successful (View mode)',
                    data: {
                        token: this.viewToken,
                        sid: this.sessionId,
                        ucc: this.ucc,
                        greetingName: this.greetingName,
                        kType: response.data.data.kType || 'View',
                        canTrade: false
                    }
                };
            } else {
                throw new Error(response.data?.message || 'Authentication failed');
            }
        } catch (error) {
            console.error('Authentication error:', error);
            this.isAuthenticated = false;
            this.accessToken = null;
            
            if (error.response?.status === 401) {
                return { success: false, message: 'Invalid UCC, mobile number, or TOTP code' };
            } else if (error.response?.status === 400) {
                return { success: false, message: 'Invalid request format. Please check your credentials.' };
            } else if (error.response?.status === 429) {
                return { success: false, message: 'Too many requests. Please try again later.' };
            } else {
                return { success: false, message: error.message || 'Authentication failed' };
            }
        }
    }

    /**
     * Step 3: Authenticate for Trading with MPIN (Get Trading Token)
     */
    async authenticateForTrading(mpin) {
        try {
            if (!this.viewToken || !this.sessionId) {
                throw new Error('Please authenticate with TOTP first');
            }

            // Validate MPIN format
            if (!/^\d{4,6}$/.test(mpin)) {
                throw new Error('MPIN must be 4-6 digits');
            }

            console.log('Authenticating for trading with MPIN...');

            const tradingData = {
                mpin: mpin
            };

            const response = await axios.post(
                this.validateURL,
                tradingData,
                {
                    headers: {
                        'accept': 'application/json',
                        'sid': this.sessionId,
                        'Auth': this.viewToken,
                        'neo-fin-key': 'neotradeapi',
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.clientAccessToken}`
                    },
                    timeout: 15000
                }
            );

            if (response.data && response.data.data && response.data.data.token) {
                // Update with trading token
                this.tradingToken = response.data.data.token;
                this.sessionId = response.data.data.sid;
                this.accessToken = this.tradingToken; // Use trading token as active token
                this.canTrade = true;
                this.saveTokens();

                console.log('✓ Trading authentication successful');
                
                return {
                    success: true,
                    message: 'Trading authentication successful',
                    data: {
                        token: this.tradingToken,
                        sid: this.sessionId,
                        ucc: this.ucc,
                        kType: response.data.data.kType || 'Trade',
                        canTrade: true
                    }
                };
            } else {
                throw new Error(response.data?.message || 'Trading authentication failed');
            }
        } catch (error) {
            console.error('Trading authentication error:', error);
            
            if (error.response?.status === 401) {
                return { success: false, message: 'Invalid MPIN' };
            } else {
                return { success: false, message: error.message || 'Trading authentication failed' };
            }
        }
    }

    /**
     * Get authentication headers for API requests
     */
    getAuthHeaders() {
        if (!this.isAuthenticated || !this.accessToken) {
            throw new Error('Not authenticated');
        }

        const headers = {
            'Authorization': `Bearer ${this.clientAccessToken}`,
            'Auth': this.accessToken,
            'neo-fin-key': 'neotradeapi',
            'Content-Type': 'application/json'
        };

        if (this.sessionId) {
            headers['sid'] = this.sessionId;
        }

        return headers;
    }

    /**
     * Check if session is valid
     */
    isSessionValid() {
        if (!this.isAuthenticated || !this.accessToken || !this.sessionExpiry) {
            return false;
        }
        return new Date() < this.sessionExpiry;
    }

    /**
     * Save tokens to storage
     */
    saveTokens() {
        try {
            this.store.set('tokens', {
                clientAccessToken: this.clientAccessToken,
                viewToken: this.viewToken,
                tradingToken: this.tradingToken,
                accessToken: this.accessToken,
                sessionId: this.sessionId,
                requestId: this.requestId,
                ucc: this.ucc,
                greetingName: this.greetingName,
                sessionExpiry: this.sessionExpiry,
                isAuthenticated: this.isAuthenticated,
                canTrade: this.canTrade
            });
        } catch (error) {
            console.error('Failed to save tokens:', error);
        }
    }

    /**
     * Load stored tokens
     */
    loadStoredTokens() {
        try {
            const tokens = this.store.get('tokens');
            if (tokens && tokens.accessToken && tokens.sessionExpiry) {
                const now = new Date();
                const expiry = new Date(tokens.sessionExpiry);
                
                if (now < expiry) {
                    this.clientAccessToken = tokens.clientAccessToken;
                    this.viewToken = tokens.viewToken;
                    this.tradingToken = tokens.tradingToken;
                    this.accessToken = tokens.accessToken;
                    this.sessionId = tokens.sessionId;
                    this.requestId = tokens.requestId;
                    this.ucc = tokens.ucc;
                    this.greetingName = tokens.greetingName;
                    this.sessionExpiry = expiry;
                    this.isAuthenticated = tokens.isAuthenticated;
                    this.canTrade = tokens.canTrade;
                    console.log('✓ Loaded valid session from storage');
                } else {
                    console.log('Stored session has expired');
                    this.clearStoredTokens();
                }
            }
        } catch (error) {
            console.error('Failed to load stored tokens:', error);
        }
    }

    /**
     * Clear stored tokens
     */
    clearStoredTokens() {
        this.store.delete('tokens');
        this.clientAccessToken = null;
        this.viewToken = null;
        this.tradingToken = null;
        this.accessToken = null;
        this.sessionId = null;
        this.requestId = null;
        this.ucc = null;
        this.greetingName = null;
        this.sessionExpiry = null;
        this.isAuthenticated = false;
        this.canTrade = false;
    }

    /**
     * Logout
     */
    async logout() {
        this.clearStoredTokens();
        console.log('Logged out successfully');
        return { success: true, message: 'Logged out successfully' };
    }

    /**
     * Test connection with current credentials
     */
    async testConnection() {
        try {
            const credentials = this.getCredentials();
            if (!credentials) {
                return { success: false, message: 'No credentials configured' };
            }

            // Test by getting client access token
            await this.getClientAccessToken();
            return { success: true, message: 'Connection test successful' };
        } catch (error) {
            return { success: false, message: error.message || 'Connection test failed' };
        }
    }
}

module.exports = KotakAuthService;
