@echo off
echo ========================================
echo  Nifty Options Trading Bot Setup
echo ========================================
echo.

echo [1/5] Installing dependencies...
echo Running: npm install
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)
echo ✓ Dependencies installed successfully
echo.

echo [2/5] Validating application setup...
echo Running: npm run validate:app
call npm run validate:app
if %errorlevel% neq 0 (
    echo WARNING: Application validation found issues
    echo You may continue, but please review the warnings above
)
echo ✓ Application validation completed
echo.

echo [3/5] Running tests...
echo Running: npm test
call npm test
if %errorlevel% neq 0 (
    echo WARNING: Some tests failed
    echo You may continue, but please review the test results above
)
echo ✓ Tests completed
echo.

echo [4/5] Starting the application...
echo Running: npm start
echo.
echo NOTE: The application will start in a new window
echo       Configure your API credentials in the Settings panel
echo       Press Ctrl+C in this window to stop the application
echo.
call npm start

echo.
echo [5/5] Setup completed!
echo.
echo Next steps:
echo 1. Configure your Kotak Neo Trade API credentials in Settings
echo 2. Test the connection using the "Test Connection" button
echo 3. Authenticate with your TOTP code
echo 4. Start trading!
echo.
echo To build for distribution, run: npm run build
echo.
pause
