const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
    // Authentication
    authenticate: (credentials) => ipcRenderer.invoke('authenticate', credentials),
    
    // Trading operations
    placeOrder: (orderData) => ipcRenderer.invoke('place-order', orderData),
    cancelOrder: (orderId) => ipcRenderer.invoke('cancel-order', orderId),
    modifyOrder: (orderId, modifications) => ipcRenderer.invoke('modify-order', orderId, modifications),
    
    // Market data
    getMarketData: (symbols) => ipcRenderer.invoke('get-market-data', symbols),
    subscribeToMarketData: (symbols) => ipcRenderer.invoke('subscribe-market-data', symbols),
    unsubscribeFromMarketData: (symbols) => ipcRenderer.invoke('unsubscribe-market-data', symbols),
    
    // Portfolio and positions
    getPortfolio: () => ipc<PERSON>enderer.invoke('get-portfolio'),
    getPositions: () => ipcRenderer.invoke('get-positions'),
    getOrderBook: () => ipcRenderer.invoke('get-order-book'),
    
    // Settings
    saveSettings: (settings) => ipcRenderer.invoke('save-settings', settings),
    getSettings: () => ipcRenderer.invoke('get-settings'),
    
    // Option chain
    getOptionChain: (symbol) => ipcRenderer.invoke('get-option-chain', symbol),
    
    // Trading engine
    startTradingEngine: () => ipcRenderer.invoke('start-trading-engine'),
    stopTradingEngine: () => ipcRenderer.invoke('stop-trading-engine'),
    getTradingStatus: () => ipcRenderer.invoke('get-trading-status'),
    
    // Event listeners
    onMarketDataUpdate: (callback) => {
        ipcRenderer.on('market-data-update', callback);
        return () => ipcRenderer.removeListener('market-data-update', callback);
    },
    
    onOrderUpdate: (callback) => {
        ipcRenderer.on('order-update', callback);
        return () => ipcRenderer.removeListener('order-update', callback);
    },
    
    onPortfolioUpdate: (callback) => {
        ipcRenderer.on('portfolio-update', callback);
        return () => ipcRenderer.removeListener('portfolio-update', callback);
    },
    
    onTradingEngineUpdate: (callback) => {
        ipcRenderer.on('trading-engine-update', callback);
        return () => ipcRenderer.removeListener('trading-engine-update', callback);
    },
    
    onNotification: (callback) => {
        ipcRenderer.on('notification', callback);
        return () => ipcRenderer.removeListener('notification', callback);
    },
    
    // Menu events
    onOpenSettings: (callback) => {
        ipcRenderer.on('open-settings', callback);
        return () => ipcRenderer.removeListener('open-settings', callback);
    },
    
    onStartTrading: (callback) => {
        ipcRenderer.on('start-trading', callback);
        return () => ipcRenderer.removeListener('start-trading', callback);
    },
    
    onStopTrading: (callback) => {
        ipcRenderer.on('stop-trading', callback);
        return () => ipcRenderer.removeListener('stop-trading', callback);
    },
    
    onViewPortfolio: (callback) => {
        ipcRenderer.on('view-portfolio', callback);
        return () => ipcRenderer.removeListener('view-portfolio', callback);
    },
    
    // Utility functions
    showNotification: (title, body) => ipcRenderer.invoke('show-notification', { title, body }),
    showErrorDialog: (title, content) => ipcRenderer.invoke('show-error-dialog', { title, content }),
    showConfirmDialog: (title, content) => ipcRenderer.invoke('show-confirm-dialog', { title, content }),
    
    // File operations
    exportData: (data, filename) => ipcRenderer.invoke('export-data', { data, filename }),
    importData: (fileType) => ipcRenderer.invoke('import-data', fileType),
    
    // Logging
    logInfo: (message) => ipcRenderer.invoke('log-info', message),
    logError: (message) => ipcRenderer.invoke('log-error', message),
    logWarning: (message) => ipcRenderer.invoke('log-warning', message)
});

// Expose version information
contextBridge.exposeInMainWorld('appInfo', {
    version: process.env.npm_package_version || '1.0.0',
    platform: process.platform,
    arch: process.arch
});
